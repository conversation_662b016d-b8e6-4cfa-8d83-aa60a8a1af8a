#!/usr/bin/env node

/**
 * Test script for memory optimization
 * This script tests the image processing utilities to ensure they work correctly
 */

const { 
  processImageOptimized, 
  processImagesInBatches, 
  getCacheStats, 
  monitorMemoryUsage 
} = require('./app/utils/image-processing.utils');

const { convertMultipleImagesToBase64 } = require('./app/utils/common-function.utils');

async function testMemoryOptimization() {
  console.log('🧪 Testing Memory Optimization...\n');
  
  // Monitor initial memory
  console.log('📊 Initial Memory Usage:');
  monitorMemoryUsage();
  console.log('');
  
  // Test image URLs (using placeholder images)
  const testImageUrls = [
    'https://via.placeholder.com/800x600/FF0000/FFFFFF?text=Image1',
    'https://via.placeholder.com/800x600/00FF00/FFFFFF?text=Image2',
    'https://via.placeholder.com/800x600/0000FF/FFFFFF?text=Image3',
    'https://via.placeholder.com/800x600/FFFF00/FFFFFF?text=Image4',
    'https://via.placeholder.com/800x600/FF00FF/FFFFFF?text=Image5'
  ];
  
  try {
    console.log('🖼️  Testing single image processing...');
    const singleImage = await processImageOptimized(testImageUrls[0]);
    console.log(`✅ Single image processed: ${singleImage ? 'Success' : 'Failed'}`);
    
    console.log('\n📦 Testing batch image processing...');
    const batchImages = await processImagesInBatches(testImageUrls, 2);
    console.log(`✅ Batch processing completed: ${batchImages.length}/${testImageUrls.length} images processed`);
    
    console.log('\n🔄 Testing common function wrapper...');
    const commonImages = await convertMultipleImagesToBase64(testImageUrls.slice(0, 3), 2);
    console.log(`✅ Common function processing: ${commonImages.length}/3 images processed`);
    
    console.log('\n📈 Cache Statistics:');
    const cacheStats = getCacheStats();
    console.log(`- Cache size: ${cacheStats.size}/${cacheStats.maxSize}`);
    console.log(`- Memory used: ${Math.round(cacheStats.memoryUsage.heapUsed / 1024 / 1024)}MB`);
    
    console.log('\n📊 Final Memory Usage:');
    monitorMemoryUsage();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('\n💡 Memory optimization is working correctly.');
    console.log('   You can now safely generate PDFs with many images.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Check your internet connection');
    console.log('2. Ensure Sharp library is properly installed');
    console.log('3. Verify Node.js version compatibility');
  }
}

// Run the test
if (require.main === module) {
  testMemoryOptimization().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { testMemoryOptimization };
