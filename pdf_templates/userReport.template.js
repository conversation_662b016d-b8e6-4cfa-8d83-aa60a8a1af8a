const commonFunctionsUtils = require('../app/utils/common-function.utils');

exports.userReport = async templateData => {
  const { companyPrimaryColor, requestData } = templateData;
  const { reportQuestions, userReportStatus, exportReportStatus } = requestData;
  const reportName = requestData.report;
  let reportAnswerContent = await this.prepareReportAnswers(reportQuestions, exportReportStatus);
  let userSignContent = this.prepareUserSignContent(userReportStatus);
  let assetResult;

  if (requestData.assets.length > 0) {
    assetResult = await this.assetData(requestData.assets);
  }

  return `
  <!DOCTYPE html>
    <html lang="en">
    
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report</title>
        <style>
            html {
                box-sizing: border-box;
            }
    
            *,
            *::before,
            *::after {
                box-sizing: inherit;
            }
    
            :root {
                --primary-color: ${companyPrimaryColor};
                --primary-font-color: #323232;
                --info-title-color: #4D5464;
                --info-desc-color: #333843;
                --status-color: #009A38;
                --circle-bg-color: #D9D9D9;
                --black-color: #000000;
                --info-label-color: #FFFFFF;
                --table-border: #E0E6F5;
                --table-header-border: #E0E6F51A;
                --white-color: #FFFFFF;
                --pdf-bg-color: #F6F7FF;
            }
    
            body {
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                font-family: Arial, sans-serif;
                width: 100%;
                height: 100%;
                color: var(--primary-font-color);
            }
    
            .main {
                width: 100%;
            }
    
            .pdf-header {
                display: flex;
                width: 100%;
            }
    
            .pdf-header-title {
                font-size: 24px;
                padding: 0;
                margin: 0;
                margin-bottom: 10px;
                font-weight: 700;
            }
    
            .space-container {
                height: 10px;
            }
    
            .custom-table {
                width: 100%;
                border-spacing: 0px;
                border-radius: 4px;
            }
    
            .custom-table tr {
                page-break-inside: avoid;
            }
    
            .custom-table th {
                text-align: left;
                padding-left: 10px;
                background-color: var(--primary-color);
                color: var(--white-color);
            }

            .answerType-background {
                background-color: rgb(232 233 238) !important;
                color: var(--primary-color) !important;
            }
    
            .custom-table td {
                padding-left: 10px;
            }
    
            .custom-table th:first-child {
                border-right: 1px solid var(--table-border);
                border-top-left-radius: 4px;
            }
    
            .custom-table td:first-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
                border-left: 1px solid var(--table-border);
            }
    
            .custom-table th:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
            }
    
            .custom-table td:not(:first-child):not(:last-child) {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }
    
            .custom-table th:last-child {
                border-right: 0px;
                border-top-right-radius: 4px;
            }
    
            .custom-table td:last-child {
                border-right: 1px solid var(--table-border);
                border-bottom: 1px solid var(--table-border);
            }
    
            .custom-table tr:last-child td:first-child {
                border-bottom-left-radius: 4px;
            }
    
            .custom-table tr:last-child td:last-child {
                border-bottom-right-radius: 4px;
            }
            
            .custom-table-image-header {
                text-align: left;
                padding: 10px;
                background-color: var(--primary-color);
                color: var(--white-color);
                font-weight: bold;
            }
    
            .safety-table-header {
                padding: 10px;
                font-size: 14px;
            }
    
            .safety-first-table-desc {
                padding: 5px;
                font-size: 16px;
                font-weight: 500;
            }
    
            .full-info-container {
                width: 100%;
                display: flex;
                flex-direction: column;
            }
    
            .full-info-container-title {
                width: 100%;
                display: flex;
                flex-direction: column;
                padding: 10px 10px;
                border-radius: 2px;
                background-color: var(--primary-color);
                color: var(--white-color);
                font-weight: bold;
                font-size: 14px;
                margin-top: 5px;
            }
    
            .full-info-container-desc {
                width: 100%;
                padding: 10px 10px;
                border: 2px;
                text-align: justify;
                font-weight: 500;
                font-size: 16px;
                background-color: var(--white-color);
            }
    
            .image-table-td {
                width: 33.33%;
                height: 300px;
                padding: 10px;
                text-align: center;
                vertical-align: middle;
            }

            .image-bg-container {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 100%;
              height: 100%;
              border-radius: 2px;
              overflow: hidden; /* hides overflow if image is larger */
            }

            .image-bg-container-img {
              max-width: 100%;
              max-height: 100%;
              width: auto;
              height: auto;
              display: block;
              border-radius: 2px;
            }

            .text-line {
                display: flex;
                align-items: center;
                margin-top: 20px;
            }
    
            .text-line p {
                margin: 0;
                padding-right: 10px;
                white-space: nowrap;
                font-size: 18px;
                font-weight: 700;
                color: var(--primary-color);
            }
    
            .text-line hr {
                flex-grow: 1;
                border: none;
                border-top: 1px solid var(--primary-color);
                margin: 0;
            }
    
            .flex-div-container {
                display: flex;
                justify-content: space-between;
                gap: 5px;
            }
    
            .custom-checkbox {
                width: 14px;
                height: 14px;
                border: 1px solid var(--primary-font-color);
                border-radius: 3px;
            }
    
            .custom-checkbox span {
                display: none;
            }
    
            .custom-checked-checkbox {
                width: 14px;
                height: 14px;
                border: 1px solid var(--primary-font-color);
                border-radius: 3px;
                background-color: var(--primary-color);
                color: var(--white-color);
                display: flex;
                justify-content: center;
                align-items: center;
                padding-top: 1px;
            }
    
            .sign-container {
                width: 80px;
                height: 20px;
                display: flex;
                justify-content: end;
                align-items: center;
            }
    
            .margin-bottom {
                margin-bottom: 5px;
            }
    
            .image-answer-container {
                display: flex;
                flex-wrap: wrap;
                gap: 10px;
                margin-top: 10px;
                margin-left: 10px;
            }
    
            .image-answer {
                width: 100px;
                height: 100px;
                border-radius: 4px;
                object-fit: contain;
            }
    
            .custom-hr {
                flex-grow: 1;
                border: none;
                border-top: 1px solid var(--table-border);
                margin: 0 10px;
            }
        </style>
    </head>
    
    <body>
        <div class="main">
            <div class="pdf-header">
                <p class="pdf-header-title">Report - ${reportName}</p>
            </div>
            <table class="custom-table">
                <tr>
                    <th class="safety-table-header">Project</th>
                    <th class="safety-table-header">Report</th>
                    <th class="safety-table-header">Location</th>
                </tr>
                <tr>
                    <td class="safety-first-table-desc" style="width:33.33%;">${
                      requestData.project ?? ''
                    }</td>
                    <td class="safety-first-table-desc" style="width:33.33%;">${
                      requestData.report
                    }</td>
                    <td class="safety-first-table-desc" style="width:33.33%;">${
                      requestData.location ?? ''
                    }</td>
                </tr>
            </table>
             ${assetResult || ''}
            <div class="space-container"></div>
            ${reportAnswerContent}
            <div class="space-container"></div>
            <div class="space-container"></div>
            ${userSignContent ?? ''}
            <div class="space-container"></div>
            <div class="space-container"></div>
        </div>
    </body>
    
    </html>
      `;
};

function capitalizeFirstLetterOfTypes(type) {
  return `${type.replace(/^./, type[0].toUpperCase())} Details`;
}

/**
 * Prepare report answers
 *
 * @param {*} questionData
 * @returns
 */
exports.prepareReportAnswers = async (questionData, exportReportStatus) => {
  const useLowMemoryMode =
    process.env.LOW_MEMORY_MODE === 'true' || process.env.NODE_ENV === 'low-memory';
  console.log(useLowMemoryMode);

  if (useLowMemoryMode) {
    const { processImagesLowMemory } = require('../app/utils/low-memory-pdf.utils');
    return processImagesLowMemory(questionData, exportReportStatus);
  }

  let htmlContent = '';

  // Collect all image URLs first for batch processing
  const imageUrls = [];
  const imageMap = new global.Map(); // Map to track image positions

  // First pass: collect all image URLs
  for (let data of questionData) {
    if (data.supportedContent) {
      for (let supported of data.supportedContent) {
        if (supported.isPrintable || exportReportStatus === 'pdfAll') {
          // Extract image URLs from supported content if any
          // This will be handled in the second pass
        }
      }
    }

    if (data.answers) {
      for (let answer of data.answers) {
        if (answer.type === 'image' && answer.answers) {
          for (let answerItem of answer.answers) {
            if (answerItem.answer && typeof answerItem.answer === 'string') {
              const imageUrl = await commonFunctionsUtils.urlReplacement(answerItem.answer);
              if (imageUrl && !imageUrls.includes(imageUrl)) {
                imageUrls.push(imageUrl);
                imageMap.set(imageUrl, imageUrls.length - 1);
              }
            }
          }
        }
      }
    }
  }

  // Process all images in batches
  let processedImages = [];
  if (imageUrls.length > 0) {
    console.log(`Processing ${imageUrls.length} images in batches...`);
    processedImages = await commonFunctionsUtils.convertMultipleImagesToBase64(imageUrls, 3); // Process 3 images at a time
  }

  // Create image processing context to pass to displayAnswers
  // Convert Map to object for easier access
  const imageMapObj = {};
  imageMap.forEach((value, key) => {
    imageMapObj[key] = value;
  });

  const imageContext = {
    imageMap: imageMapObj,
    processedImages,
  };

  for (let data of questionData) {
    htmlContent += `
        <div class="space-container"></div>
        <span class="full-info-container-title" style="margin-top: 20px;">
        ${data.title}
        </span>
      `;
    if (data.supportedContent) {
      for (let supported of data.supportedContent) {
        if (supported.isPrintable || exportReportStatus === 'pdfAll') {
          htmlContent += `
            <table style="border: 1px solid #ddd; width: 100%; padding: 0px; border-collapse: separate; border-radius: 8px;">
              <tr style="page-break-inside: avoid;">
                <td style="padding-left: 10px;">
                  <div class="space-container"></div>
                  <span style="white-space: pre-wrap;">${supported.subText}</span>
                </td>
              </tr>
              ${
                supported.path !== ''
                  ? `
                  <tr style="page-break-inside: avoid;">
                    <td class="image-answer-container" style="text-align: center; padding-top: 10px;">
                      <img class="image-answer" src="${supported.path}" alt="item" style="display: block; width: 50%; height: auto; margin: 0 auto;">
                    </td>
                  </tr>`
                  : ''
              }
            </table>`;
        } else {
          htmlContent += '';
        }
      }
    }
    if (data.answers) {
      if (data.supportedContent && data.supportedContent.length > 0) {
        htmlContent += `
        <div style="page-break-inside: avoid; page-break-before: always;"> 
        <div class="space-container"></div>
        <span class="full-info-container-title" style="margin-top: 20px;">
        ${data.title}
        </span>
        </div>
      `;
      }
      for (let answer of data.answers) {
        let totalAnswers = answer.answers.length;
        if (totalAnswers > 0) {
          htmlContent += `
          <div class="space-container"></div>
              <div>
                  <div class="text-line" style="display: none;">
                      <p>${capitalizeFirstLetterOfTypes(answer.name)}</p>
                      <hr>
                  </div>
                  <div class="space-container"></div>
                  `;
          htmlContent += await exports.displayAnswers(answer, imageContext);
          htmlContent += '</div>';
        }
      }
    }
  }

  return htmlContent;
};

exports.displayAnswers = async (answersData, imageContext = {}) => {
  if (answersData) {
    const { imageMap = {}, processedImages = [] } = imageContext;
    let htmlContent = '';
    const type = answersData.type;
    const answerTitles = answersData.answers.map(answer => answer.answerTitle);
    let answerData = answersData.answers.map(answer => answer.answer);
    if (type === 'checkbox') {
      answerData = {
        totalOptions: answersData?.options
          ?.map(option => (option.isActive ? option.title : ''))
          .filter(option => option !== ''),
        selectedOptions: answersData?.answers?.map(ans => {
          return {
            answerTitle: ans.answerTitle,
            answer: ans.answer.split('##'),
          };
        }),
      };
      const chunkSize = 3;
      for (let i = 0; i < answerTitles.length; i += chunkSize) {
        const titleChunk = answerTitles.slice(i, i + chunkSize);
        const dataChunk = answerData.selectedOptions.slice(i, i + chunkSize);

        // Create a new table for each chunk
        if (answerTitles.length > 3 && titleChunk.length === 1)
          htmlContent += `
        <table class="custom-table" style="width: 33.33%; page-break-inside: avoid;">
        <tr>
      `;
        else if (answerTitles.length > 3 && titleChunk.length === 2)
          htmlContent += `
        <table class="custom-table" style="width: 66.66%; page-break-inside: avoid;">
        <tr>
      `;
        else
          htmlContent += `
        <table class="custom-table" style="width: 100%; page-break-inside: avoid;">
        <tr>
      `;

        for (const title of titleChunk) {
          htmlContent += `<th class="safety-table-header answerType-background" style="width: calc(100% / ${titleChunk.length})">${title}</th>`;
        }
        htmlContent += '</tr><tr>';

        for (const data of dataChunk) {
          htmlContent += `
        <td class="safety-first-table-desc">
        `;
          for (const option of answerData.totalOptions) {
            const className = data.answer
              .map(item => item.toLowerCase())
              .includes(option.toLowerCase())
              ? 'custom-checked-checkbox'
              : 'custom-checkbox';
            htmlContent += `
            <div class="flex-div-container margin-bottom" >
                <span style="width: 100%;">${option}</span>
                <div class=${className}>
                    <span>&#10003</span>
                </div>
            </div>
            `;
          }

          htmlContent += '</td>';
        }
        htmlContent += '</tr></table>';
      }
    } else if (type === 'image' || type === 'signature') {
      const chunkSize = 3;
      for (let i = 0; i < answerTitles.length; i += chunkSize) {
        const titleChunk = answerTitles.slice(i, i + chunkSize);
        const dataChunk = answerData.slice(i, i + chunkSize);

        // Create a new table for each chunk
        htmlContent += `
        <table class="custom-table" style="page-break-inside: avoid; width: calc(33.33% * ${titleChunk.length});">
        <tr>
      `;

        for (const title of titleChunk) {
          htmlContent += `<th class="safety-table-header answerType-background" style="width: 33.33%">${title}</th>`;
        }
        htmlContent += '</tr><tr>';
        for (const data of dataChunk) {
          let compressImg = await commonFunctionsUtils.urlReplacement(data);
          const imageIndex = imageMap[compressImg];
          const processedImage = imageIndex !== undefined ? processedImages[imageIndex] : null;

          // Fallback to individual processing if batch processing failed
          const finalImage =
            processedImage ||
            (await commonFunctionsUtils.convertImageUrlToBase64Image(compressImg));

          htmlContent += `<td class="image-table-td">
        <div class="image-bg-container">
        ${
          finalImage
            ? `<img class="image-bg-container-img"
          src=${finalImage}
          alt="logo">`
            : ''
        }
        </div>
        </td>`;
        }
        htmlContent += '</tr></table>';
      }
    } else {
      const chunkSize = 3;
      for (let i = 0; i < answerTitles.length; i += chunkSize) {
        const titleChunk = answerTitles.slice(i, i + chunkSize);
        const dataChunk = answerData.slice(i, i + chunkSize);
        const isDateTime = type === 'dateTime';

        // Create a new table for each chunk
        if (answerTitles.length > 3 && titleChunk.length === 1)
          htmlContent += `
        <table class="custom-table" style="width: 33.33%; page-break-inside: avoid;">
        <tr>
      `;
        else if (answerTitles.length > 3 && titleChunk.length === 2)
          htmlContent += `
        <table class="custom-table" style="width: 66.66%; page-break-inside: avoid;">
        <tr>
      `;
        else
          htmlContent += `
        <table class="custom-table" style="width: 100%; page-break-inside: avoid;">
        <tr>
      `;
        for (const title of titleChunk) {
          htmlContent += `<th class="safety-table-header answerType-background" 
                          style="width: calc(100% / ${titleChunk.length}); padding: 5px; vertical-align: middle;">${title}</th>`;
        }
        htmlContent += '</tr><tr>';
        for (const data of dataChunk) {
          const formattedData = isDateTime
            ? data && (await commonFunctionsUtils.formatDateTime(data))
            : data;
          htmlContent += `<td class="safety-first-table-desc" 
                          style="padding: ${
                            formattedData === '' ? '12px;' : '5px'
                          }  vertical-align: middle;">${formattedData}</td>`;
        }
        htmlContent += '</tr></table>';
      }
    }

    return htmlContent;
  }
};

/**
 * Prepare answer data
 *
 * @param {*} answer
 * @returns
 */
exports.prepareAnswerData = async answer => {
  let htmlContent = '';

  // Collect image URLs for batch processing if answer type is image
  let processedImages = [];
  if (answer.type === 'image') {
    const imageUrls = [];
    for (let answerItem of answer.answers) {
      if (answerItem.answer) {
        const imageUrl = await commonFunctionsUtils.urlReplacement(answerItem.answer);
        imageUrls.push(imageUrl);
      }
    }

    if (imageUrls.length > 0) {
      processedImages = await commonFunctionsUtils.convertMultipleImagesToBase64(imageUrls, 3);
    }
  }

  // Loop through all the answers
  for (let i = 0; i < answer.answers.length; i++) {
    // Start a new row every three items
    if (i % 3 === 0) {
      htmlContent += '<tr>';
    }

    // Add table cells with the answer title and image
    htmlContent += `
            <td class="safety-table-header" style="width: 33.33%;">
                <div class="custom-table-image-header">${answer.answers[i].answerTitle}</div>
                <div class="safety-table-header">`;
    // Check if answer type is "image"
    if (answer.type === 'image') {
      // Use pre-processed image or fallback to individual processing
      let compressImg = processedImages[i] || null;

      if (!compressImg) {
        let imageUrl = await commonFunctionsUtils.urlReplacement(answer.answers[i].answer);
        compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(imageUrl);

        if (!compressImg) {
          compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(
            answer.answers[i].answer
          );
        }
      }

      htmlContent += `<img src="${compressImg}" style="max-width: 100%;">`;
    } else {
      htmlContent += `${answer.answers[i].answer}`;
    }
    htmlContent += `</div>
            </td>`;

    // End the row after three items or if it's the last item
    if ((i + 1) % 3 === 0 || i + 1 === answer.answers.length) {
      htmlContent += '</tr>';
    }
  }
  return htmlContent;
};

/**
 * Prepare answer rows
 *
 * @param {*} answer
 * @param {*} type
 * @returns
 */
exports.prepareAnswerRowsOnType = async (answer, type) => {
  let htmlContent = '';
  // Add table data cells
  if (type === 'image') {
    let compressImg = await commonFunctionsUtils.urlReplacement(answer);
    compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(compressImg);
    if (!compressImg) {
      compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(answer);
    }

    htmlContent += `<td class="image-table-td">
                        <div class="image-bg-container">
                            <img class="image-bg-container-img" src="${compressImg}" alt="logo">
                        </div>
                    </td>`;
  } else if (type === 'checkbox') {
    htmlContent += `<td class="safety-first-table-desc">
                          <div class="flex-div-container">
                              <span>${answer}</span>
                                <div class="custom-checked-checkbox">
                                    <span>&#10003</span>
                                </div>
                          </div>
                        </td>`;
  } else {
    htmlContent += `<td class="safety-first-table-desc">
                          <div class="flex-div-container">
                              <span></span>
                              <span>${answer}</span>
                          </div>
                        </td>`;
  }

  return htmlContent;
};

/**
 * Prepare user sign content
 *
 * @param {*} userData
 * @returns
 */
exports.prepareUserSignContent = userData => {
  if (userData) {
    let htmlContent = `
    <table class="custom-table" style="width: 100%; page-break-inside: avoid;">
      <tr>
        <th colspan="3" class="safety-table-header" style="border-right: 0px; text-align: left;">
          Sign-Off
        </th>
      </tr>`;

    // eslint-disable-next-line no-undef
    const uniqueSignatures = new Map();

    for (let item of userData) {
      if (item.signature === null) continue;
      const { _id } = item.createdBy;

      if (
        !uniqueSignatures.has(_id.toString()) ||
        new Date(item.createdAt) > new Date(uniqueSignatures.get(_id.toString()).createdAt)
      ) {
        uniqueSignatures.set(_id.toString(), item);
      }
    }

    const uniqueSignaturesArray = Array.from(uniqueSignatures.values());

    for (let i = 0; i < uniqueSignaturesArray.length; i += 3) {
      htmlContent += '<tr>';

      const remainingItems = uniqueSignaturesArray.length - i;
      const columnsInThisRow = Math.min(remainingItems, 3);
      const cellWidth = `${100 / columnsInThisRow}%`;

      for (let j = 0; j < columnsInThisRow; j++) {
        const { firstName, callingName, lastName } = uniqueSignaturesArray[i + j]?.signatureBy || {
          callingName: '',
          firstName: '',
          lastName: '',
        };

        if (
          (callingName !== '' || firstName !== '') &&
          uniqueSignaturesArray[i + j].signature !== ''
        ) {
          htmlContent += `
        <td class="safety-first-table-desc" style="width: ${cellWidth}; padding: 10px; text-align: left;">
          <div class="flex-div-container" style="display: flex; align-items: center; gap: 15px;">
            <div style="display: flex; flex-direction: column;">
              <span style="font-weight: bold;">${
                callingName !== null && callingName !== undefined && callingName !== ''
                  ? callingName
                  : firstName
              } ${lastName}</span>
              <span style="font-size: 9px; color: #888;">${commonFunctionsUtils.formatDate(
                uniqueSignaturesArray[i + j].updatedAt
              )}</span>
            </div>
            <div class="sign-container">
              ${
                uniqueSignaturesArray[i + j].signature
                  ? `<img class="image-bg-container-img" src="${
                      uniqueSignaturesArray[i + j].signature
                    }" alt="sign" style="max-width: 100px; height: auto;">`
                  : ''
              }
            </div>
          </div>
        </td>`;
        }
      }

      htmlContent += '</tr>';
    }

    htmlContent += '</table>';

    return htmlContent;
  }
};

/**
 * Prepare assets
 *
 * @param {*} assets
 * @returns
 */
exports.assetData = async assets => {
  let htmlContent = '';
  htmlContent += `<div class="space-container"></div>
                    <table class="custom-table">
                    <tr>
                      <th class="safety-table-header">Cable</th>
                      <th class="safety-table-header">Manufacturer</th>
                      <th class="safety-table-header">Type</th>
                    </tr>`;
  for (let asset of assets) {
    htmlContent += `<tr>
                        <td class="safety-first-table-desc" style="width:33.33%;">${
                          asset.cableName ? asset.cableName : ''
                        }</td>
                        <td class="safety-first-table-desc" style="width:33.33%;">${
                          asset.manufacturer ? asset.manufacturer : ''
                        }</td>
                        <td class="safety-first-table-desc" style="width:33.33%;">${
                          asset.typeMm2 ? asset.typeMm2 : ''
                        }</td>
                    </tr>`;
  }
  htmlContent += '</table>';
  return htmlContent;
};
