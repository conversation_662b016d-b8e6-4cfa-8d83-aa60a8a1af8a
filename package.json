{"name": "wtsenergy", "version": "1.0.1", "description": "", "main": "index.js", "scripts": {"test": "jest", "dev": "nodemon --max-old-space-size=4096 --expose-gc index.js", "start": "node --max-old-space-size=6144 --expose-gc index.js", "start:prod": "./start-production.sh", "start:memory-optimized": "node --max-old-space-size=6144 --optimize-for-size --expose-gc --gc-interval=100 index.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write '**/*.{js,jsx,ts,tsx,json,md}'", "prepare": "husky install", "pre-commit": "lint-staged", "seeder": "node ./app/seeders/index.js"}, "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^8.1.1", "adm-zip": "^0.5.10", "aws-sdk": "^2.1317.0", "axios": "^1.7.8", "bcryptjs": "^2.4.3", "bic-validator": "^1.27.0", "cors": "^2.8.5", "crypto-random-string": "^3.3.1", "dateformat": "^4.6.3", "dotenv": "^16.0.3", "exceljs": "^4.3.0", "express": "^4.21.2", "express-validator": "^6.14.2", "file-size": "^1.0.0", "html-pdf-node": "^1.0.8", "ipware": "^2.0.0", "jest": "^29.6.3", "jsonwebtoken": "^9.0.2", "luxon": "^3.6.1", "mongodb": "^6.11.0", "mongoose": "^8.8.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemon": "^3.0.0", "pdfkit-table": "^0.1.99", "rand-token": "^1.0.1", "sharp": "^0.33.5", "supertest": "^6.3.3", "winston": "^3.17.0", "winston-mongodb": "^6.0.0"}, "devDependencies": {"@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "lint-staged": "^13.2.1", "prettier": "2.8.7"}, "engines": {"node": "20.15.0"}}