require('dotenv').config();
require('./app/utils/global-constants.utils');
const { logger } = require('./app/utils/logger.utils');
const PORT = process.env.PORT || 8000;
const app = require('./app/server');

// Log memory configuration
const memoryUsage = process.memoryUsage();
logger.info('Memory configuration:');
logger.info(`- Heap Used: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`);
logger.info(`- Heap Total: ${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`);
logger.info(`- External: ${Math.round(memoryUsage.external / 1024 / 1024)}MB`);
logger.info(`- RSS: ${Math.round(memoryUsage.rss / 1024 / 1024)}MB`);
logger.info(`- GC Available: ${global.gc ? 'Yes' : 'No'}`);

// Set up periodic memory monitoring
setInterval(() => {
  const usage = process.memoryUsage();
  const usedMB = Math.round(usage.heapUsed / 1024 / 1024);
  const totalMB = Math.round(usage.heapTotal / 1024 / 1024);

  if (usedMB > 2000) { // Log when memory usage exceeds 2GB
    logger.warn(`High memory usage: ${usedMB}MB / ${totalMB}MB`);
  }
}, 60000); // Check every minute

module.exports = app.listen(PORT, () => {
  logger.info('server is running on port %s', PORT);
});
