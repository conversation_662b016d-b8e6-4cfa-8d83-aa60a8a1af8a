const axios = require('axios');
const { logger } = require('./logger.utils');

/**
 * Memory-optimized PDF export utility
 * This is a completely isolated solution that only affects the export PDF functionality
 * It doesn't modify any shared utilities or services
 */

/**
 * Convert image URL to base64 with memory optimization and GC
 * @param {string} url - Image URL
 * @returns {Promise<string|null>} - Base64 image or null
 */
const convertImageWithMemoryOptimization = async (url) => {
  try {
    const response = await axios.get(url, { 
      responseType: 'arraybuffer',
      timeout: 15000, // 15 second timeout
      maxContentLength: 5 * 1024 * 1024 // 5MB limit
    });
    
    const base64Image = `data:${response.headers['content-type']};base64,` + 
                       Buffer.from(response.data).toString('base64');
    
    // Clear response data immediately
    response.data = null;
    
    return base64Image;
  } catch (error) {
    if (error.code !== 'ECONNABORTED') {
      logger.error(`Error processing image ${url}: ${error.message}`);
    }
    return null;
  }
};

/**
 * Process images with aggressive garbage collection
 * @param {Array<string>} imageUrls - Array of image URLs
 * @param {number} batchSize - Batch size (default: 1 for maximum memory efficiency)
 * @returns {Promise<Array<string|null>>} - Array of base64 images
 */
const processImagesWithGC = async (imageUrls, batchSize = 1) => {
  const results = [];
  let processedCount = 0;
  
  for (let i = 0; i < imageUrls.length; i += batchSize) {
    const batch = imageUrls.slice(i, i + batchSize);
    
    // Process batch
    const batchPromises = batch.map(url => convertImageWithMemoryOptimization(url));
    const batchResults = await Promise.allSettled(batchPromises);
    
    // Extract results
    const batchImages = batchResults.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        logger.error(`Failed to process image ${batch[index]}: ${result.reason}`);
        return null;
      }
    });
    
    results.push(...batchImages);
    processedCount += batch.length;
    
    // Force garbage collection every image (or batch)
    if (global.gc) {
      global.gc();
    }
    
    // Log progress every 10 images
    if (processedCount % 10 === 0) {
      const memUsage = process.memoryUsage();
      const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
      logger.info(`Processed ${processedCount}/${imageUrls.length} images. Memory: ${heapUsedMB}MB`);
    }
    
    // Small delay to prevent overwhelming the system
    if (i + batchSize < imageUrls.length) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }
  
  return results;
};

/**
 * Replace the original prepareReportAnswers function with memory-optimized version
 * This function is specifically for the export PDF functionality
 */
const prepareReportAnswersMemoryOptimized = async (questionData, exportReportStatus) => {
  let htmlContent = '';
  
  for (let data of questionData) {
    htmlContent += `
        <div class="space-container"></div>
        <span class="full-info-container-title" style="margin-top: 20px;">
        ${data.title}
        </span>
      `;
    
    if (data.supportedContent) {
      for (let supported of data.supportedContent) {
        if (supported.isPrintable || exportReportStatus === 'pdfAll') {
          htmlContent += `
            <table style="border: 1px solid #ddd; width: 100%; padding: 0px; border-collapse: separate; border-radius: 8px;">
              <tr style="page-break-inside: avoid;">
                <td style="padding-left: 10px;">
                  <div class="space-container"></div>
                  <span class="full-info-container-title">${supported.title}</span>
                  <div class="space-container"></div>
                  <span class="full-info-container-desc">${supported.description}</span>
                  <div class="space-container"></div>
                </td>
              </tr>
            </table>`;
        }
      }
    }

    if (data.answers) {
      for (let answer of data.answers) {
        let totalAnswers = answer.answers.length;
        if (totalAnswers > 0) {
          htmlContent += `
          <div class="space-container"></div>
              <div>
                  <div class="text-line" style="display: none;">
                      <p>${capitalizeFirstLetterOfTypes(answer.name)}</p>
                      <hr>
                  </div>
                  <div class="space-container"></div>
                  `;
          
          // Process this answer's images with memory optimization
          htmlContent += await displayAnswersMemoryOptimized(answer);
          htmlContent += '</div>';
          
          // Force GC after each answer section
          if (global.gc && answer.type === 'image') {
            global.gc();
          }
        }
      }
    }
  }
  
  return htmlContent;
};

/**
 * Memory-optimized version of displayAnswers
 */
const displayAnswersMemoryOptimized = async (answersData) => {
  if (!answersData) return '';
  
  let htmlContent = '';
  const type = answersData.type;
  const answerTitles = answersData.answers.map(answer => answer.answerTitle);
  let answerData = answersData.answers.map(answer => answer.answer);
  
  if (type === 'checkbox') {
    // Handle checkbox (no images, no memory concern)
    answerData = {
      totalOptions: answersData?.options
        ?.map(option => (option.isActive ? option.title : ''))
        .filter(option => option !== ''),
      selectedOptions: answersData?.answers?.map(ans => {
        return {
          answerTitle: ans.answerTitle,
          answer: ans.answer.split('##'),
        };
      }),
    };
    
    const chunkSize = 3;
    for (let i = 0; i < answerTitles.length; i += chunkSize) {
      const titleChunk = answerTitles.slice(i, i + chunkSize);
      const dataChunk = answerData.selectedOptions.slice(i, i + chunkSize);

      htmlContent += `<table class="custom-table" style="width: 100%; page-break-inside: avoid;"><tr>`;
      
      for (const title of titleChunk) {
        htmlContent += `<th class="safety-table-header answerType-background" style="width: calc(100% / ${titleChunk.length})">${title}</th>`;
      }
      htmlContent += '</tr><tr>';

      for (const data of dataChunk) {
        htmlContent += `<td class="safety-first-table-desc">`;
        for (const option of answerData.totalOptions) {
          const className = data.answer
            .map(item => item.toLowerCase())
            .includes(option.toLowerCase())
            ? 'custom-checked-checkbox'
            : 'custom-checkbox';
          htmlContent += `
            <div class="flex-div-container margin-bottom">
                <span style="width: 100%;">${option}</span>
                <div class=${className}>
                    <span>&#10003</span>
                </div>
            </div>`;
        }
        htmlContent += '</td>';
      }
      htmlContent += '</tr></table>';
    }
  } else if (type === 'image' || type === 'signature') {
    // Process images one by one with immediate memory cleanup
    const chunkSize = 3;
    for (let i = 0; i < answerTitles.length; i += chunkSize) {
      const titleChunk = answerTitles.slice(i, i + chunkSize);
      const dataChunk = answerData.slice(i, i + chunkSize);

      htmlContent += `
        <table class="custom-table" style="page-break-inside: avoid; width: calc(33.33% * ${titleChunk.length});">
        <tr>`;

      for (const title of titleChunk) {
        htmlContent += `<th class="safety-table-header answerType-background" style="width: 33.33%">${title}</th>`;
      }
      htmlContent += '</tr><tr>';
      
      // Process each image individually with immediate cleanup
      for (const data of dataChunk) {
        const commonFunctionsUtils = require('./common-function.utils');
        let compressImg = await commonFunctionsUtils.urlReplacement(data);
        
        // Process image with memory optimization
        let finalImage = await convertImageWithMemoryOptimization(compressImg);
        
        htmlContent += `<td class="image-table-td">
        <div class="image-bg-container">
        ${
          finalImage
            ? `<img class="image-bg-container-img" src="${finalImage}" alt="logo">`
            : ''
        }
        </div>
        </td>`;
        
        // Clear variables immediately
        finalImage = null;
        compressImg = null;
        
        // Force GC after each image
        if (global.gc) {
          global.gc();
        }
      }
      htmlContent += '</tr></table>';
    }
  } else {
    // Handle other types (text, datetime, etc.)
    const chunkSize = 3;
    for (let i = 0; i < answerTitles.length; i += chunkSize) {
      const titleChunk = answerTitles.slice(i, i + chunkSize);
      const dataChunk = answerData.slice(i, i + chunkSize);
      const isDateTime = type === 'dateTime';

      htmlContent += `<table class="custom-table" style="width: 100%; page-break-inside: avoid;"><tr>`;
      
      for (const title of titleChunk) {
        htmlContent += `<th class="safety-table-header answerType-background" 
                        style="width: calc(100% / ${titleChunk.length}); padding: 5px; vertical-align: middle;">${title}</th>`;
      }
      htmlContent += '</tr><tr>';
      
      for (const data of dataChunk) {
        const commonFunctionsUtils = require('./common-function.utils');
        const formattedData = isDateTime
          ? data && (await commonFunctionsUtils.formatDateTime(data))
          : data;
        htmlContent += `<td class="safety-first-table-desc" 
                        style="padding: ${
                          formattedData === '' ? '12px;' : '5px'
                        }  vertical-align: middle;">${formattedData}</td>`;
      }
      htmlContent += '</tr></table>';
    }
  }

  return htmlContent;
};

/**
 * Helper function to capitalize first letter
 */
function capitalizeFirstLetterOfTypes(type) {
  return `${type.replace(/^./, type[0].toUpperCase())} Details`;
}

module.exports = {
  prepareReportAnswersMemoryOptimized,
  displayAnswersMemoryOptimized,
  processImagesWithGC,
  convertImageWithMemoryOptimization
};
