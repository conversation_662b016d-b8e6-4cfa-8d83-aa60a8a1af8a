const axios = require('axios');
const sharp = require('sharp');
const { logger } = require('./logger.utils');

// Image cache to avoid reprocessing same images
const imageCache = new Map();
const MAX_CACHE_SIZE = 100; // Maximum number of cached images
const MAX_IMAGE_SIZE = 2 * 1024 * 1024; // 2MB max per image
const COMPRESSION_QUALITY = 80; // JPEG quality
const MAX_DIMENSION = 1200; // Max width/height

/**
 * Clean up image cache when it gets too large
 */
const cleanupCache = () => {
  if (imageCache.size > MAX_CACHE_SIZE) {
    const keysToDelete = Array.from(imageCache.keys()).slice(0, imageCache.size - MAX_CACHE_SIZE);
    keysToDelete.forEach(key => imageCache.delete(key));

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }
};

/**
 * Process and compress image with memory optimization
 * @param {string} imageUrl - URL of the image to process
 * @param {Object} options - Processing options
 * @returns {Promise<string|null>} - Base64 encoded image or null
 */
const processImageOptimized = async (imageUrl, options = {}) => {
  try {
    // Check cache first
    if (imageCache.has(imageUrl)) {
      return imageCache.get(imageUrl);
    }

    const {
      maxWidth = MAX_DIMENSION,
      maxHeight = MAX_DIMENSION,
      quality = COMPRESSION_QUALITY,
      format = 'jpeg',
    } = options;

    // Download image with size limit
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
      maxContentLength: MAX_IMAGE_SIZE,
      timeout: 10000, // 10 second timeout
    });

    if (!response.data || response.data.length === 0) {
      logger.warn(`Empty image data for URL: ${imageUrl}`);
      return null;
    }

    // Process image with Sharp for better memory management
    let processedBuffer;
    try {
      const sharpInstance = sharp(response.data);
      const metadata = await sharpInstance.metadata();

      // Skip processing if image is already small enough
      if (
        metadata.width <= maxWidth &&
        metadata.height <= maxHeight &&
        response.data.length < MAX_IMAGE_SIZE / 2
      ) {
        processedBuffer = response.data;
      } else {
        // Resize and compress
        processedBuffer = await sharpInstance
          .resize(maxWidth, maxHeight, {
            fit: 'inside',
            withoutEnlargement: true,
          })
          .jpeg({ quality, progressive: true })
          .toBuffer();
      }
    } catch (sharpError) {
      logger.warn(`Sharp processing failed for ${imageUrl}, using original: ${sharpError.message}`);
      processedBuffer = response.data;
    }

    // Convert to base64
    const contentType = response.headers['content-type'] || 'image/jpeg';
    const base64Image = `data:${contentType};base64,${processedBuffer.toString('base64')}`;

    // Cache the result
    imageCache.set(imageUrl, base64Image);
    cleanupCache();

    // Clear local variables to help GC
    processedBuffer = null;

    return base64Image;
  } catch (error) {
    logger.error(`Error processing image ${imageUrl}: ${error.message}`);
    return null;
  }
};

/**
 * Process multiple images in batches to manage memory
 * @param {Array<string>} imageUrls - Array of image URLs
 * @param {number} batchSize - Number of images to process simultaneously
 * @param {Object} options - Processing options
 * @returns {Promise<Array<string|null>>} - Array of base64 images
 */
const processImagesInBatches = async (imageUrls, batchSize = 5, options = {}) => {
  const results = [];

  for (let i = 0; i < imageUrls.length; i += batchSize) {
    const batch = imageUrls.slice(i, i + batchSize);

    logger.info(
      `Processing image batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
        imageUrls.length / batchSize
      )}`
    );

    // Process batch concurrently
    const batchPromises = batch.map(url => processImageOptimized(url, options));
    const batchResults = await Promise.allSettled(batchPromises);

    // Extract results and handle failures
    const batchImages = batchResults.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        logger.error(`Failed to process image ${batch[index]}: ${result.reason}`);
        return null;
      }
    });

    results.push(...batchImages);

    // Force garbage collection between batches if available
    if (global.gc && i % (batchSize * 2) === 0) {
      global.gc();
    }

    // Small delay to prevent overwhelming the system
    if (i + batchSize < imageUrls.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  return results;
};

/**
 * Clear image cache
 */
const clearImageCache = () => {
  imageCache.clear();
  if (global.gc) {
    global.gc();
  }
};

/**
 * Get cache statistics
 */
const getCacheStats = () => {
  return {
    size: imageCache.size,
    maxSize: MAX_CACHE_SIZE,
    memoryUsage: process.memoryUsage(),
  };
};

/**
 * Monitor memory usage and log warnings
 */
const monitorMemoryUsage = () => {
  const usage = process.memoryUsage();
  const usedMB = Math.round(usage.heapUsed / 1024 / 1024);
  const totalMB = Math.round(usage.heapTotal / 1024 / 1024);

  logger.info(
    `Memory usage: ${usedMB}MB / ${totalMB}MB (${Math.round((usedMB / totalMB) * 100)}%)`
  );

  // Warning if memory usage is high
  if (usedMB > 3000) {
    // 3GB warning threshold
    logger.warn(`High memory usage detected: ${usedMB}MB`);

    // Clear cache if memory is very high
    if (usedMB > 4000) {
      // 4GB critical threshold
      logger.warn('Critical memory usage - clearing image cache');
      clearImageCache();
    }
  }

  return usage;
};

module.exports = {
  processImageOptimized,
  processImagesInBatches,
  clearImageCache,
  getCacheStats,
  monitorMemoryUsage,
};
