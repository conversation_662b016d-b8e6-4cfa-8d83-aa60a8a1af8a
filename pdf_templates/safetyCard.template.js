const commonUtils = require('../app/utils/common.utils');
const commonFunctionsUtils = require('../app/utils/common-function.utils');

/**
 * Generate safety card pdf
 *
 * @param {*} templateData
 * @returns
 */
exports.safetyCardTemplate = async templateData => {
  const { companyPrimaryColor, requestData } = templateData;

  let images;
  let dynamicFields;
  if (requestData.images.length > 0) {
    images = await this.getImage(requestData.images);
  }

  if (requestData.dynamicFields.length > 0) {
    dynamicFields = await this.getDynamicFields(requestData.dynamicFields);
  }

  const projectTitle = requestData?.project?.projectNumber
    ? `${requestData.project.projectNumber} - ${requestData.project.title}`
    : requestData?.project?.title ?? 'N/A';
  const cardType = await commonUtils.alterStringFromRequestString(requestData.cardType);
  const title = requestData?.title ?? 'N/A';
  const locationTitle = requestData?.location?.title ?? 'N/A';
  const riskFactor = requestData.riskFactor;
  const createdBy = requestData.createdBy;
  const productQuality = requestData.productQuality;
  const status = await commonUtils.alterStringFromRequestString(requestData.status);
  const statusTextColor = this.getStatusTextColor(status);
  const description = requestData.description === '' ? 'N/A' : requestData.description;
  const actionRecommendation = requestData.actionsTaken === '' ? 'N/A' : requestData.actionsTaken;
  const createdAt = await commonUtils.convertUTCToLocalTimezone(
    requestData.createdAt,
    requestData.userTimezone,
    global.constant.DATE_FORMAT_DD_MM_YYYY_HH_MM
  );
  const correctiveAction =
    requestData.correctiveAction === '' ? 'N/A' : requestData.correctiveAction;
  const preventiveAction =
    requestData.preventiveAction === '' ? 'N/A' : requestData.preventiveAction;
  const estimatedDelayCost =
    requestData.estimatedDelayCost === '' ? 'N/A' : requestData.estimatedDelayCost;
  const category = requestData?.category?.categoryName ? requestData.category.categoryName : 'N/A';
  const cardLogs = await this.getCardData(requestData.cardLogs, requestData.userTimezone);

  return `
    <!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Safety Card</title>
            <style>
                html {
                    box-sizing: border-box;
                }

                *,
                *::before,
                *::after {
                    box-sizing: inherit;
                }

                :root {
                    --primary-color: ${companyPrimaryColor};
                    --primary-font-color: #323232;
                    --info-title-color: #4D5464;
                    --info-desc-color: #333843;
                    --status-color: #009A38;
                    --circle-bg-color: #D9D9D9;
                    --black-color: #000000;
                    --info-label-color: #FFFFFF;
                    --table-border: #E0E6F5;
                    --table-header-border: #E0E6F51A;
                    --white-color: #FFFFFF;
                    --pdf-bg-color: #F6F7FF;
                }

                body {
                    margin: 0;
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    font-family: Arial, sans-serif;
                    width: 100%;
                    height: 100%;
                    color: var(--primary-font-color);
                    font-size: 12px;
                    font-weight: 500;
                }

                .main {
                    width: 100%;
                }

                .pdf-header {
                    display: flex;
                    width: 100%;
                }

                .pdf-header-title {
                    font-size: 20px;
                    padding: 0;
                    margin: 0;
                    margin-bottom: 10px;
                    font-weight: 600;
                }

                .space-container {
                    height: 20px;
                }

                .custom-table {
                    width: 100%;
                    border-spacing: 0px;
                    border-radius: 4px;
                }

                .custom-table tr {
                    page-break-inside: avoid;
                }

                .custom-table th {
                    text-align: left;
                    padding-left: 10px;
                    background-color: var(--primary-color);
                    color: var(--white-color);
                }

                .custom-table td {
                    padding-left: 10px;
                }

                .custom-table th:first-child {
                    border-right: 1px solid var(--table-border);
                    border-top-left-radius: 4px;
                }

                .custom-table td:first-child {
                    border-right: 1px solid var(--table-border);
                    border-bottom: 1px solid var(--table-border);
                    border-left: 1px solid var(--table-border);
                }

                .custom-table th:not(:first-child):not(:last-child) {
                    border-right: 1px solid var(--table-border);
                }

                .custom-table td:not(:first-child):not(:last-child) {
                    border-right: 1px solid var(--table-border);
                    border-bottom: 1px solid var(--table-border);
                }

                .custom-table th:last-child {
                    border-right: 0px;
                    border-top-right-radius: 4px;
                }

                .custom-table td:last-child {
                    border-right: 1px solid var(--table-border);
                    border-bottom: 1px solid var(--table-border);
                }

                .custom-table tr:last-child td:first-child {
                    border-bottom-left-radius: 4px;
                }

                .custom-table tr:last-child td:last-child {
                    border-bottom-right-radius: 4px;
                }

                .image-table {
                    width: 100%;
                    border-spacing: 0;
                    border-collapse: collapse;
                }

                .image-table tr {
                    page-break-inside: avoid;
                }

                .image-table td {
                    width: 33.33%;
                    height: 330px;
                    padding: 20px;
                    text-align: center;
                    vertical-align: middle;
                }

                .image-table td:first-child {
                    padding-left: 0px;
                }

                .image-table td:nth-child(2) {
                    padding: 10px;
                }

                .image-table td:last-child {
                    padding-right: 0px;
                }

                .image-table tr:last-child td {
                    padding-bottom: 20px;
                    padding-top: 10px;
                }

                .image-bg-container {
                    background-color: var(--table-border);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    border-radius: 8px;
                    height: 330px;

                }

                .image-table img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                }

                .info-header-style {
                    padding: 10px;
                    font-size: 12px;
                     font-weight: 600;
                    width: calc(100%/3);
                }

                .info-desc-style {
                    padding: 15px;
                    font-size: 12px;
                    font-weight: 500;
                }
                .info-tick-style  {
                    padding: 15px;
                    font-size: 18px;
                    font-weight: 500;
                }
                .info-desc-created-by {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 12px;
                    font-weight: 600;
                }
                .info-product-quality {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 12px;
                    font-weight: normal;
                }

                .full-info-container {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    page-break-inside: avoid;
                    break-inside: avoid;
                }

                .full-info-container-title {
                    width: 100%;
                    padding: 10px 10px;
                    border-radius: 2px;
                    background-color: var(--primary-color);
                    color: var(--white-color);
                    font-weight: 600;
                    font-size: 12px;
                    margin-top: 5px;
                }

                .full-info-container-desc {
                    width: 100%;
                    padding: 10px 10px;
                    border: 2px;
                    text-align: justify;
                    font-weight: 500;
                    font-size: 12px;
                    background-color: var(--white-color);
                    overflow-wrap: break-word;
                    page-break-inside: avoid;
                    break-inside: avoid;
                }

                #safety-table-title {
                    width: 100%;
                    font-size: 12px;
                    font-weight: 600;
                    margin-bottom: 22px;
                }

                .safety-table-header {
                    padding: 13px;
                    font-size: 12px;
                    width: calc(100%/3);
                }

                #safety-table-header-no {
                    width: 50px;
                }

                #safety-table-header-updated-by {
                    width: 125px;
                }

                #safety-table-header-version {
                    width: 80px;
                }

                #safety-table-header-status {
                    width: 100px;
                }

                .safety-table-td {
                    padding: 15px;
                    font-size: 12px;
                    font-weight: 500;
                }

                #safety-table-td-created-by {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                .safety-table-td-reason {
                    text-align: justify;
                }
            </style>
        </head>

        <body>
            <div class="main">
                <div class="pdf-header">
                    <p class="pdf-header-title">${cardType === 'Ncr' ? 'NCR' : cardType} Card</p>
                </div>
                <table class="custom-table">
                    <tr>
                        <th class="info-header-style">Project</th>
                        <th class="info-header-style">Title</th>
                        <th class="info-header-style">Location</th>
                    </tr>
                    <tr>
                        <td class="info-desc-style">${projectTitle ?? 'N/A'}</td>
                        <td class="info-desc-style">${title ?? 'N/A'}</td>
                        <td class="info-desc-style">${locationTitle ?? 'N/A'}</td>
                    </tr>
                </table>
                <div class="space-container"></div>
                <table class="custom-table">
                    <tr>
                    ${
                      cardType === 'Ncr'
                        ? ''
                        : `<th class="info-header-style">${
                            cardType === 'Safe' ? 'Category' : 'Risk Factor'
                          }</th>`
                    }
                        <th class="info-header-style">Created By</th>
                        <th class="info-header-style">Product Quality</th>
                        <th class="info-header-style">Status</th>
                    </tr>
                    <tr>
                    ${
                      cardType === 'Ncr'
                        ? ''
                        : ` <td class="info-desc-style">${
                            cardType === 'Safe' ? category : riskFactor
                          }</td>`
                    }
                        <td class="info-desc-style">
                            <div class="info-desc-created-by">
                                <span>${await commonUtils.alterStringFromRequestString(
                                  createdBy.callingName
                                    ? createdBy.callingName
                                    : createdBy.firstName ?? 'N/A'
                                )} ${await commonUtils.alterStringFromRequestString(
    createdBy.lastName ?? 'N/A'
  )}</span>
                                <span>${createdAt}</span>
                            </div>
                        </td>
                      <td class="info-desc-style">
                        ${
                          productQuality
                            ? '<div class="info-desc-created-by"><span><svg width="24" height="24" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="CheckIcon"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"></path></svg></span></div>'
                            : '<div class="info-product-quality"><span>N/A</span></div>'
                        }
                      </td>
                       <td class="info-desc-style" style="color: ${statusTextColor};">${
    status ?? 'N/A'
  }</td>
                    </tr>
                </table>
                <div class="space-container"></div>
                <div>
                ${
                  cardType !== 'Safe' && cardType !== 'Ncr'
                    ? `
                    <div class="full-info-container">
                      <span class="full-info-container-title">Category</span>
                      <span class="full-info-container-desc">${category}</span>
                    </div>
                    <div class="space-container"></div>
                    `
                    : ''
                }
                    <div class="full-info-container">
                        <span class="full-info-container-title">Description</span>
                        <span class="full-info-container-desc">
                       ${description ?? 'N/A'}
                        </span>
                    </div>
                    <div class="space-container"></div>
                    ${
                      cardType === 'Ncr'
                        ? `<div class="full-info-container">
                        <span class="full-info-container-title">Corrective Actions / Recommendations</span>
                        <span class="full-info-container-desc">
                           ${correctiveAction}
                        </span>
                    </div>
                    <div class="space-container"></div>
                    <div class="full-info-container">
                        <span class="full-info-container-title">Preventive Actions / Recommendations</span>
                        <span class="full-info-container-desc">${preventiveAction}</span>
                    </div>
                    <div class="space-container"></div>
                     <div class="full-info-container">
                        <span class="full-info-container-title">Estimated Delay / Cost</span>
                        <span class="full-info-container-desc">${estimatedDelayCost}</span>
                    </div>`
                        : `<div class="full-info-container">
                        <span class="full-info-container-title">Actions / Recommendations</span>
                        <span class="full-info-container-desc">
                           ${actionRecommendation}
                        </span>
                    </div>`
                    }

                    ${
                      images
                        ? `
                        <div style="page-break-before: always; margin: 0; padding: 0"></div>
                        <div class="full-info-container">
                            <span class="full-info-container-title">Images</span>
                        </div>
                        <table class="image-table">
                            <tbody>
                                ${images}
                            </tbody>
                        </table>
                        `
                        : ''
                    }
                    ${
                      cardLogs
                        ? `<div style="page-break-before: always; margin: 0; padding: 0"></div>
                    <p class="full-info-container-title" id="safety-table-title">Status Update History</p>
                    <table class="custom-table">
                        <thead>
                            <tr>
                                <th class="safety-table-header" id="safety-table-header-no">No</th>
                                <th class="safety-table-header" id="safety-table-header-updated-by">Updated By</th>
                                <th class="safety-table-header" id="safety-table-header-version">Version</th>
                                <th class="safety-table-header" id="safety-table-header-status">Status</th>
                                <th class="safety-table-header">Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                           ${cardLogs}
                        </tbody>
                    </table>`
                        : ''
                    }

                ${
                  dynamicFields
                    ? `<div style="page-break-before: always; margin: 0; padding: 0"></div>
                    <p class="full-info-container-title" id="safety-table-title">Dynamic Fields</p>
                     <table class="custom-table">
                        <thead>
                            <tr>
                                <th class="safety-table-header" id="safety-table-header-no">No</th>
                                <th class="safety-table-header" id="safety-table-header-updated-by">Title</th>
                                <th class="safety-table-header" id="safety-table-header-updated-by">Field Name</th>
                                <th class="safety-table-header" id="safety-table-header-version">Field Type</th>
                                <th class="safety-table-header" id="safety-table-header-status">Values</th>
                            </tr>
                        </thead>
                        <tbody>
                           ${dynamicFields}
                        </tbody>
                    </table>`
                    : ''
                }
                </div>
        </body>
        </html>
    `;
};

/**
 * Image template
 *
 * @param {*} image
 * @returns
 */
exports.getImage = async image => {
  let rows = '';
  let count = 0;

  for (let i = 0; i < image.length; i++) {
    if (count === 0) {
      rows += '<tr>';
    }
    let compressImg = await commonFunctionsUtils.urlReplacement(image[i].url);
    compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(compressImg);

    if (!compressImg) {
      compressImg = await commonFunctionsUtils.convertImageUrlToBase64Image(image[i].url);
    }

    rows += `
          <td>
              <div class="image-bg-container">
                  <img src="${compressImg}" alt="logo">
              </div>
              <p style="margin-bottom: 0px;">${image[i].name}</p>
          </td>`;

    count++;

    if (count === 3 || i === image.length - 1) {
      rows += '</tr>';
      count = 0;
    }
  }
  return rows;
};

/**
 * Get card data
 *
 * @param {*} cardData
 * @returns
 */
exports.getCardData = async (cardData, userTimeZone) => {
  let rows = '';
  let count = 0;
  for (const element of cardData) {
    count++;
    rows += `<tr>
            <td class="safety-table-td">${count}</td>
            <td class="safety-table-td">
                <div id="safety-table-td-created-by">
                    <span>
                        ${await commonUtils.alterStringFromRequestString(
                          element.user.callingName
                            ? element.user.callingName
                            : element.user.firstName ?? 'N/A'
                        )} ${await commonUtils.alterStringFromRequestString(
      element.user.lastName
    )}</span>
                    <span>${await commonUtils.convertUTCToLocalTimezone(
                      element.time,
                      userTimeZone,
                      global.constant.DATE_FORMAT_DD_MM_YYYY_HH_MM
                    )}</span>
                </div>
            </td>
            <td class="safety-table-td">${await commonUtils.alterStringFromRequestString(
              element.version
            )}</td>
            <td class="safety-table-td">${await commonUtils.alterStringFromRequestString(
              element.status
            )}</td>
            <td class="safety-table-td safety-table-td-reason">${element.action}</td>
        </tr>`;
  }
  return rows;
};

/**
 * Get Dynamic Fields
 *
 * @param {*} dynamicFields
 * @returns
 */
exports.getDynamicFields = async dynamicFields => {
  let rows = '';
  let count = 0;
  for (const element of dynamicFields) {
    count++;
    rows += `<tr>
              <td class="safety-table-td">${count}</td>
              <td class="safety-table-td">
                  <div id="safety-table-td-created-by">
                      <span>
                          ${await commonUtils.alterStringFromRequestString(element.title)}</span>
                  </div>
              </td>
              <td class="safety-table-td">${await commonUtils.alterStringFromRequestString(
                element.fieldId.fieldName
              )}</td>
              <td class="safety-table-td">${await commonUtils.alterStringFromRequestString(
                element.fieldId.fieldType
              )}</td>
              <td class="safety-table-td safety-table-td-reason">${element.value.join(', ')}</td>
          </tr>`;
  }

  return rows;
};

exports.getStatusTextColor = status => {
  if (!status) return '#000000';
  switch (status.trim()) {
    case 'Open':
      return '#9E2C0D';
    case 'Submitted':
      return '#E96215';
    case 'In Discussion':
      return '#BB890F';
    case 'Closed':
      return '#54931C';
    case 'Archived':
      return '#475467';
    default:
      return '#000000';
  }
};
