/* eslint-disable quotes */
const { logger } = require('./logger.utils');
const commonFunctionsUtils = require('./common-function.utils');

/**
 * Low memory PDF generation utility
 * This approach processes images one by one and immediately releases memory
 * Suitable for servers with limited memory (4GB or less)
 */

/**
 * Process images with minimal memory footprint
 * @param {Array} questionData - Question data containing images
 * @param {string} exportReportStatus - Export status
 * @returns {string} HTML content with processed images
 */
exports.processImagesLowMemory = async (questionData, exportReportStatus) => {
  let htmlContent = '';
  let processedImageCount = 0;

  logger.info('Starting low-memory image processing...');

  for (let data of questionData) {
    htmlContent += `
        <div class="space-container"></div>
        <span class="full-info-container-title" style="margin-top: 20px;">
        ${data.title}
        </span>
      `;

    if (data.supportedContent) {
      for (let supported of data.supportedContent) {
        if (supported.isPrintable || exportReportStatus === 'pdfAll') {
          htmlContent += `
            <table style="border: 1px solid #ddd; width: 100%; padding: 0px; border-collapse: separate; border-radius: 8px;">
              <tr style="page-break-inside: avoid;">
                <td style="padding-left: 10px;">
                  <div class="space-container"></div>`;

          htmlContent += `
                  <span class="full-info-container-title">${supported.title}</span>
                  <div class="space-container"></div>
                  <span class="full-info-container-desc">${supported.description}</span>
                  <div class="space-container"></div>
                </td>
              </tr>
            </table>`;
        }
      }
    }

    if (data.answers) {
      for (let answer of data.answers) {
        let totalAnswers = answer.answers.length;
        if (totalAnswers > 0) {
          htmlContent += `
          <div class="space-container"></div>
              <div>
                  <div class="text-line" style="display: none;">
                      <p>${capitalizeFirstLetterOfTypes(answer.name)}</p>
                      <hr>
                  </div>
                  <div class="space-container"></div>
                  `;

          // Process images one by one for this answer
          htmlContent += await exports.displayAnswersLowMemory(answer);
          htmlContent += '</div>';

          // Force garbage collection every 10 images if available
          if (answer.type === 'image') {
            processedImageCount += answer.answers.length;
            if (global.gc && processedImageCount % 10 === 0) {
              global.gc();
              logger.info(`Processed ${processedImageCount} images, memory cleaned`);
            }
          }
        }
      }
    }
  }

  logger.info(`Low-memory processing completed. Total images: ${processedImageCount}`);
  return htmlContent;
};

/**
 * Display answers with low memory usage
 * @param {Object} answersData - Answer data
 * @returns {string} HTML content
 */
exports.displayAnswersLowMemory = async answersData => {
  if (!answersData) return '';

  let htmlContent = '';
  const type = answersData.type;
  const answerTitles = answersData.answers.map(answer => answer.answerTitle);
  let answerData = answersData.answers.map(answer => answer.answer);

  if (type === 'checkbox') {
    // Handle checkbox type (no images, so no memory concern)
    answerData = {
      totalOptions: answersData?.options
        ?.map(option => (option.isActive ? option.title : ''))
        .filter(option => option !== ''),
      selectedOptions: answersData?.answers?.map(ans => {
        return {
          answerTitle: ans.answerTitle,
          answer: ans.answer.split('##'),
        };
      }),
    };

    const chunkSize = 3;
    for (let i = 0; i < answerTitles.length; i += chunkSize) {
      const titleChunk = answerTitles.slice(i, i + chunkSize);
      const dataChunk = answerData.selectedOptions.slice(i, i + chunkSize);

      // eslint-disable-next-line quotes
      htmlContent += `<table class="custom-table" style="width: 100%; page-break-inside: avoid;"><tr>`;

      for (const title of titleChunk) {
        htmlContent += `<th class="safety-table-header answerType-background" style="width: calc(100% / ${titleChunk.length})">${title}</th>`;
      }
      htmlContent += '</tr><tr>';

      for (const data of dataChunk) {
        htmlContent += `<td class="safety-first-table-desc">`;
        for (const option of answerData.totalOptions) {
          const className = data.answer
            .map(item => item.toLowerCase())
            .includes(option.toLowerCase())
            ? 'custom-checked-checkbox'
            : 'custom-checkbox';
          htmlContent += `
            <div class="flex-div-container margin-bottom">
                <span style="width: 100%;">${option}</span>
                <div class=${className}>
                    <span>&#10003</span>
                </div>
            </div>`;
        }
        htmlContent += '</td>';
      }
      htmlContent += '</tr></table>';
    }
  } else if (type === 'image' || type === 'signature') {
    // Process images one by one to minimize memory usage
    const chunkSize = 3;
    for (let i = 0; i < answerTitles.length; i += chunkSize) {
      const titleChunk = answerTitles.slice(i, i + chunkSize);
      const dataChunk = answerData.slice(i, i + chunkSize);

      htmlContent += `
        <table class="custom-table" style="page-break-inside: avoid; width: calc(33.33% * ${titleChunk.length});">
        <tr>`;

      for (const title of titleChunk) {
        htmlContent += `<th class="safety-table-header answerType-background" style="width: 33.33%">${title}</th>`;
      }
      htmlContent += '</tr><tr>';

      // Process each image individually
      for (const data of dataChunk) {
        let compressImg = await commonFunctionsUtils.urlReplacement(data);

        // Process image immediately and don't store in memory
        let finalImage = await commonFunctionsUtils.convertImageUrlToBase64Image(compressImg);

        htmlContent += `<td class="image-table-td">
        <div class="image-bg-container">
        ${finalImage ? `<img class="image-bg-container-img" src=${finalImage} alt="logo">` : ''}
        </div>
        </td>`;

        // Clear the image variable immediately
        finalImage = null;
        compressImg = null;
      }
      htmlContent += '</tr></table>';
    }
  } else {
    // Handle other types (text, datetime, etc.)
    const chunkSize = 3;
    for (let i = 0; i < answerTitles.length; i += chunkSize) {
      const titleChunk = answerTitles.slice(i, i + chunkSize);
      const dataChunk = answerData.slice(i, i + chunkSize);
      const isDateTime = type === 'dateTime';

      htmlContent += `<table class="custom-table" style="width: 100%; page-break-inside: avoid;"><tr>`;

      for (const title of titleChunk) {
        htmlContent += `<th class="safety-table-header answerType-background" 
                        style="width: calc(100% / ${titleChunk.length}); padding: 5px; vertical-align: middle;">${title}</th>`;
      }
      htmlContent += '</tr><tr>';

      for (const data of dataChunk) {
        const formattedData = isDateTime
          ? data && (await commonFunctionsUtils.formatDateTime(data))
          : data;
        htmlContent += `<td class="safety-first-table-desc" 
                        style="padding: ${
                          formattedData === '' ? '12px;' : '5px'
                        }  vertical-align: middle;">${formattedData}</td>`;
      }
      htmlContent += '</tr></table>';
    }
  }

  return htmlContent;
};

/**
 * Capitalize first letter of types
 * @param {string} str - String to capitalize
 * @returns {string} Capitalized string
 */
function capitalizeFirstLetterOfTypes(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

module.exports = exports;
