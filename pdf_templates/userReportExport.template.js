const { prepareReportAnswersMemoryOptimized } = require('../app/utils/pdf-export-memory-optimized.utils');
const commonFunctionsUtils = require('../app/utils/common-function.utils');

/**
 * Memory-optimized user report template specifically for export PDF functionality
 * This template is isolated and doesn't affect other parts of the system
 */

exports.userReportExport = async templateData => {
  const { companyPrimaryColor, requestData } = templateData;
  const { reportQuestions, userReportStatus, exportReportStatus } = requestData;
  const reportName = requestData.report;
  
  // Use memory-optimized report answers processing
  let reportAnswerContent = await prepareReportAnswersMemoryOptimized(reportQuestions, exportReportStatus);
  let userSignContent = this.prepareUserSignContent(userReportStatus);
  let assetResult;

  if (requestData.assets.length > 0) {
    assetResult = await this.assetData(requestData.assets);
  }

  return `
  <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid ${companyPrimaryColor || '#007bff'};
                padding-bottom: 20px;
            }
            .report-title {
                font-size: 24px;
                font-weight: bold;
                color: ${companyPrimaryColor || '#007bff'};
                margin-bottom: 10px;
            }
            .space-container {
                height: 10px;
            }
            .full-info-container-title {
                font-size: 18px;
                font-weight: bold;
                color: #333;
                margin: 15px 0 10px 0;
                display: block;
            }
            .full-info-container-desc {
                font-size: 14px;
                color: #666;
                margin-bottom: 15px;
                line-height: 1.4;
            }
            .custom-table {
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
                background-color: white;
                border: 1px solid #ddd;
            }
            .safety-table-header {
                background-color: ${companyPrimaryColor || '#007bff'};
                color: white;
                padding: 12px 8px;
                text-align: center;
                font-weight: bold;
                border: 1px solid #ddd;
            }
            .answerType-background {
                background-color: ${companyPrimaryColor || '#007bff'} !important;
            }
            .safety-first-table-desc {
                padding: 10px 8px;
                border: 1px solid #ddd;
                text-align: center;
                vertical-align: middle;
            }
            .image-table-td {
                padding: 10px;
                border: 1px solid #ddd;
                text-align: center;
                vertical-align: middle;
            }
            .image-bg-container {
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
            .image-bg-container-img {
                max-width: 100%;
                max-height: 200px;
                object-fit: contain;
                border-radius: 4px;
            }
            .custom-table-image-header {
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
            }
            .flex-div-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 5px;
            }
            .margin-bottom {
                margin-bottom: 8px;
            }
            .custom-checkbox, .custom-checked-checkbox {
                width: 20px;
                height: 20px;
                border: 2px solid #ddd;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 3px;
                font-size: 14px;
                font-weight: bold;
            }
            .custom-checked-checkbox {
                background-color: ${companyPrimaryColor || '#007bff'};
                color: white;
                border-color: ${companyPrimaryColor || '#007bff'};
            }
            .text-line {
                margin: 20px 0;
                padding: 10px 0;
            }
            .text-line hr {
                border: none;
                height: 1px;
                background-color: #ddd;
                margin: 10px 0;
            }
            .sign-container {
                display: flex;
                justify-content: center;
                align-items: center;
            }
            @media print {
                body {
                    background-color: white;
                    padding: 0;
                }
                .container {
                    box-shadow: none;
                    border-radius: 0;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="report-title">${reportName}</div>
                <div style="font-size: 14px; color: #666;">
                    Generated on ${new Date().toLocaleDateString()}
                </div>
            </div>
            
            ${reportAnswerContent}
            
            ${assetResult || ''}
            
            ${userSignContent || ''}
            
            <div class="space-container"></div>
            <div class="space-container"></div>
        </div>
    </body>
    </html>
      `;
};

/**
 * Prepare user sign content
 */
exports.prepareUserSignContent = userData => {
  if (userData) {
    let htmlContent = `
    <table class="custom-table" style="width: 100%; page-break-inside: avoid;">
      <tr>
        <th colspan="3" class="safety-table-header" style="border-right: 0px; text-align: left;">
          Sign-Off
        </th>
      </tr>`;

    const uniqueSignatures = new Map();

    for (let item of userData) {
      if (item.signature === null) continue;
      const { _id } = item.createdBy;

      if (
        !uniqueSignatures.has(_id.toString()) ||
        new Date(item.createdAt) > new Date(uniqueSignatures.get(_id.toString()).createdAt)
      ) {
        uniqueSignatures.set(_id.toString(), item);
      }
    }

    const uniqueSignaturesArray = Array.from(uniqueSignatures.values());

    for (let i = 0; i < uniqueSignaturesArray.length; i += 3) {
      htmlContent += '<tr>';

      const remainingItems = uniqueSignaturesArray.length - i;
      const columnsInThisRow = Math.min(remainingItems, 3);
      const cellWidth = `${100 / columnsInThisRow}%`;

      for (let j = 0; j < columnsInThisRow; j++) {
        const { firstName, callingName, lastName } = uniqueSignaturesArray[i + j]?.signatureBy || {
          callingName: '',
          firstName: '',
          lastName: '',
        };

        if (
          (callingName !== '' || firstName !== '') &&
          uniqueSignaturesArray[i + j].signature !== ''
        ) {
          htmlContent += `
        <td class="safety-first-table-desc" style="width: ${cellWidth}; padding: 10px; text-align: left;">
          <div class="flex-div-container" style="display: flex; align-items: center; gap: 15px;">
            <div style="display: flex; flex-direction: column;">
              <span style="font-weight: bold;">${
                callingName !== null && callingName !== undefined && callingName !== ''
                  ? callingName
                  : firstName
              } ${lastName}</span>
              <span style="font-size: 9px; color: #888;">${commonFunctionsUtils.formatDate(
                uniqueSignaturesArray[i + j].updatedAt
              )}</span>
            </div>
            <div class="sign-container">
              ${
                uniqueSignaturesArray[i + j].signature
                  ? `<img class="image-bg-container-img" src="${
                      uniqueSignaturesArray[i + j].signature
                    }" alt="sign" style="max-width: 100px; height: auto;">`
                  : ''
              }
            </div>
          </div>
        </td>`;
        }
      }

      htmlContent += '</tr>';
    }

    htmlContent += '</table>';
    return htmlContent;
  }
};

/**
 * Prepare assets
 */
exports.assetData = async assets => {
  let htmlContent = '';
  htmlContent += `<div class="space-container"></div>
                    <table class="custom-table">
                    <tr>
                      <th class="safety-table-header">Cable</th>
                      <th class="safety-table-header">Manufacturer</th>
                      <th class="safety-table-header">Type</th>
                    </tr>`;
  for (let asset of assets) {
    htmlContent += `<tr>
                        <td class="safety-first-table-desc" style="width:33.33%;">${
                          asset.cableName ? asset.cableName : ''
                        }</td>
                        <td class="safety-first-table-desc" style="width:33.33%;">${
                          asset.manufacturer ? asset.manufacturer : ''
                        }</td>
                        <td class="safety-first-table-desc" style="width:33.33%;">${
                          asset.typeMm2 ? asset.typeMm2 : ''
                        }</td>
                    </tr>`;
  }
  htmlContent += '</table>';
  return htmlContent;
};
